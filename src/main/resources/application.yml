mp:
  openapi:
    extensions:
      smallrye:
        info:
          title: Authentication API
          version: 1.0.0
application:
  keycloakServerUri: ${KEYCLOAK_SERVER_URL:https://identity-stage.glideyoke.com}
  defaultTenantId: ${DEFAULT_TENANT_ID:master}
  caching:
    enabled: false
  triggerVerifyEmail: ${TRIGGER_VERIFY_EMAIL:false}
  triggerNotifyEmail: ${TRIGGER_NOTIFY_EMAIL:false}
db:
  addresses: ${DB_SERVER:bolt://localhost:7687}
  database: ${DB_NAME:neo4j}
  type: ${DB_TYPE:neo4j}
  username: ${DB_USERNAME:neo4j}
  password: ${DB_PASSWORD:neo4j}
  query:
    defaultSize: 10
quarkus:
  app:
    name: auth-service
  oidc:
    authentication:
      cookie-domain: ${COOKIE_DOMAIN:glideyoke.com}
    internalClientSecret: ${INTERNAL_CLIENT_SECRET:MGLxMiU13MBtO8dQPUFMaLaO7ZV3ql9a}
    # oAuth login for internal communication
    internalClientId: ${INTERNAL_CLIENT_ID:glide-service-admin}
  http:
    timeout: ${HTTP_TIMEOUT:10m}
    port: ${QUARKUS_PORT:8080}
    cors:
      ~: true
      origins: "*"
  smallrye-openapi:
    path: /api/q/openapi
    security-scheme: jwt
    security-scheme-name: apiToken
  swagger-ui:
    always-include: true
    theme: material
    path: /api
  datasource:
    db-kind: postgresql
    jdbc: false
    reactive:
      max-size: '16'
      url: ${DB_AUTH_URL:vertx-reactive:postgresql://localhost:35432/auth_db}
    username: ${DB_AUTH_USERNAME:auth}
    password: ${DB_AUTH_PASSWORD:z8swrUXftp3Zs9kBB5^sjH}
  hibernate-orm:
    database:
      generation: none
    log:
      sql: true
  kafka:
    devservices:
      enabled: false
  otel:
    traces:
      exporter: none

# client config
entity-api/mp-rest/uri: ${ENTITY_SERVICE_URL:http://localhost:8888}

security:
  keycloak:
    superAdminUsername: superadmin
    superAdminPassword: ${SUPER_ADMIN_PASSWORD:UXWnVXxQrLbYBsKNxNmz}
    systemClientsToPersistForNewRealm:
      - sp-services-internal # TODO: Remove after all services have been upgraded
      - ${INTERNAL_CLIENT_ID:glide-service-admin}
      - sp-services
    systemClients:
      - sp-services
    custom-smtp:
      host: ${CUSTOM_SMTP_HOST:}
      port: ${CUSTOM_SMTP_PORT:}
      username: ${CUSTOM_SMTP_USERNAME:}
      password: ${CUSTOM_SMTP_PASSWORD:}